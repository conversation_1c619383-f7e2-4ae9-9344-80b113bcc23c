import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart3,
  Clock,
  Target,
  FileText
} from "lucide-react";
import { GEOScoreCard } from "./GEOScoreCard";
import { GEORecommendationCard } from "./GEORecommendationCard";
import { formatDistanceToNow } from "date-fns";

interface HistoricalGEOAnalysis {
  analysisId: string;
  overallScore: number;
  promptMentionRate: number;
  citationRate: number;
  websiteOptimization: number;
  sentimentTone: number;
  aiDiscoverability: {
    score: number;
    mentions: number;
    sentiment: "positive" | "neutral" | "negative";
    topQueries: string[];
  };
  contentOptimization: {
    score: number;
    structureScore: number;
    factualClaimsScore: number;
    aiReadabilityScore: number;
  };
  competitiveAnalysis: {
    shareOfVoice: number;
    competitorMentions: { name: string; mentions: number }[];
  };
  analyzedPages: Array<{
    url: string;
    title: string;
    summary: string;
    score: number;
  }>;
  simulatedQueries: Array<{
    query: string;
    brand_mentioned: boolean;
    mention_context?: string;
    likelihood_score: number;
    reasoning: string;
  }>;
  recommendations: Array<{
    priority: "high" | "medium" | "low";
    category: string;
    title: string;
    description: string;
    impact: string;
  }>;
  analyzedAt: string;
  brandId: string;
  websiteAnalyzed: string;
  brandName: string;
  isHistorical: boolean;
  analysisVersion: string;
}

interface GEOHistoricalReportProps {
  analysis: HistoricalGEOAnalysis;
}

export function GEOHistoricalReport({
  analysis
}: GEOHistoricalReportProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      relative: formatDistanceToNow(date, { addSuffix: true }),
      absolute: date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };

  const dateInfo = formatDate(analysis.analyzedAt);

  return (
    <div className="p-6 space-y-8">

      {/* Historical context alert */}
      <Card className="border-amber-200 bg-amber-50 dark:bg-amber-950/20 dark:border-amber-800">
        <CardContent className="flex items-start gap-3 pt-6">
          <Clock className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-amber-800 dark:text-amber-200">
              Historical Report
            </h4>
            <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
              This report was generated on {dateInfo.absolute} for {analysis.websiteAnalyzed}.
              The data reflects your website&apos;s GEO performance at that time and may not represent current status.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Analysis Results */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6 h-12">
          <TabsTrigger value="overview" className="text-sm">Overview</TabsTrigger>
          <TabsTrigger value="pages" className="text-sm">Pages Analyzed</TabsTrigger>
          <TabsTrigger value="queries" className="text-sm">AI Queries</TabsTrigger>
          <TabsTrigger value="discoverability" className="text-sm">AI Discoverability</TabsTrigger>
          <TabsTrigger value="content" className="text-sm">Content Report</TabsTrigger>
          <TabsTrigger value="recommendations" className="text-sm">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-8">
          {/* Overall Score */}
          <Card className="border-gray-200 dark:border-gray-700 shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                Overall GEO Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-6">
                <div className="text-5xl font-bold">
                  <span className={getScoreColor(analysis.overallScore)}>
                    {analysis.overallScore}
                  </span>
                  <span className="text-muted-foreground text-2xl">/100</span>
                </div>
                <div className="flex-1 space-y-2">
                  <Progress value={analysis.overallScore} className="h-4" />
                  <p className="text-sm text-muted-foreground">
                    Historical performance snapshot
                  </p>
                </div>
                <Badge
                  variant={getScoreBadgeVariant(analysis.overallScore)}
                  className="px-3 py-1 text-sm font-medium"
                >
                  {analysis.overallScore >= 80 ? "Excellent" :
                   analysis.overallScore >= 60 ? "Good" : "Needs Improvement"}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Weighted Score Components */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <GEOScoreCard
              title="Prompt Mention Rate"
              score={Math.round(analysis.promptMentionRate)}
              description="40% weight - Brand appears in AI responses"
            />
            <GEOScoreCard
              title="Citation Rate"
              score={Math.round(analysis.citationRate)}
              description="20% weight - Website links in AI outputs"
            />
            <GEOScoreCard
              title="Website Optimization"
              score={analysis.websiteOptimization}
              description="30% weight - Content quality and structure"
            />
            <GEOScoreCard
              title="Sentiment/Tone"
              score={analysis.sentimentTone}
              description="10% weight - Positive brand framing"
            />
          </div>
        </TabsContent>

        <TabsContent value="pages" className="space-y-6">
          <Card className="border-gray-200 dark:border-gray-700 shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">
                Analyzed Pages ({analysis.analyzedPages.length})
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Pages that were crawled and analyzed for GEO optimization
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analysis.analyzedPages.map((page, index) => (
                  <Card key={index} className="border border-gray-100 dark:border-gray-800 hover:border-gray-200 dark:hover:border-gray-700 transition-colors">
                    <CardContent className="p-4 space-y-3">
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm text-gray-900 dark:text-white truncate">
                            {page.title}
                          </h4>
                          <p className="text-xs text-muted-foreground mt-1 break-all">
                            {page.url}
                          </p>
                        </div>
                        <Badge
                          variant={page.score >= 70 ? "default" : page.score >= 50 ? "secondary" : "destructive"}
                          className="flex-shrink-0"
                        >
                          {page.score}/100
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                        {page.summary}
                      </p>
                    </CardContent>
                  </Card>
                ))}
                {analysis.analyzedPages.length === 0 && (
                  <div className="text-center py-12">
                    <div className="text-muted-foreground">
                      <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No Pages Analyzed</p>
                      <p className="text-sm">
                        No pages were analyzed in this historical report.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="queries" className="space-y-6">
          <Card className="border-gray-200 dark:border-gray-700 shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">
                AI Query Simulation Results
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                How your brand performed in simulated AI search queries
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analysis.simulatedQueries.map((query, index) => (
                  <Card key={index} className="border border-gray-100 dark:border-gray-800 hover:border-gray-200 dark:hover:border-gray-700 transition-colors">
                    <CardContent className="p-4 space-y-3">
                      <div className="flex items-start justify-between gap-4">
                        <h4 className="font-medium text-sm text-gray-900 dark:text-white flex-1">
                          &quot;{query.query}&quot;
                        </h4>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <Badge variant={query.brand_mentioned ? "default" : "outline"}>
                            {query.brand_mentioned ? "Mentioned" : "Not Mentioned"}
                          </Badge>
                          {query.mention_context && (
                            <Badge variant={
                              query.mention_context === "positive" ? "default" :
                              query.mention_context === "neutral" ? "secondary" : "destructive"
                            }>
                              {query.mention_context}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-xs text-muted-foreground">
                          Likelihood Score: <span className="font-medium">{query.likelihood_score}/100</span>
                        </div>
                        <div className="flex-1">
                          <Progress value={query.likelihood_score} className="h-2" />
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                        {query.reasoning}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="discoverability" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="border-gray-200 dark:border-gray-700 shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Target className="h-5 w-5 text-blue-600" />
                  AI Mention Report
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Brand visibility in AI responses
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Total Mentions</span>
                    <p className="text-xs text-muted-foreground mt-1">Across all simulated queries</p>
                  </div>
                  <Badge variant="outline" className="text-lg px-3 py-1">
                    {analysis.aiDiscoverability.mentions}
                  </Badge>
                </div>
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Overall Sentiment</span>
                    <p className="text-xs text-muted-foreground mt-1">Brand perception in AI responses</p>
                  </div>
                  <Badge variant={
                    analysis.aiDiscoverability.sentiment === "positive" ? "default" :
                    analysis.aiDiscoverability.sentiment === "neutral" ? "secondary" : "destructive"
                  } className="capitalize">
                    {analysis.aiDiscoverability.sentiment}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="border-gray-200 dark:border-gray-700 shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">Top Performing Queries</CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Queries where your brand was mentioned
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analysis.aiDiscoverability.topQueries.map((query, index) => (
                    <div key={index} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                          #{index + 1}
                        </span>
                        <div className="h-1 w-1 bg-gray-300 rounded-full"></div>
                      </div>
                      <p className="text-sm text-gray-900 dark:text-white">
                        &quot;{query}&quot;
                      </p>
                    </div>
                  ))}
                  {analysis.aiDiscoverability.topQueries.length === 0 && (
                    <div className="text-center py-8">
                      <p className="text-sm text-muted-foreground">
                        No top queries available for this analysis
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Content Optimization Analysis
            </h3>
            <p className="text-sm text-muted-foreground">
              Detailed breakdown of your content&apos;s AI optimization scores
            </p>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <GEOScoreCard
              title="Structure Score"
              score={analysis.contentOptimization.structureScore}
              description="Heading hierarchy and organization"
            />
            <GEOScoreCard
              title="Factual Claims"
              score={analysis.contentOptimization.factualClaimsScore}
              description="Evidence-based content"
            />
            <GEOScoreCard
              title="AI Readability"
              score={analysis.contentOptimization.aiReadabilityScore}
              description="Optimized for AI consumption"
            />
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              GEO Optimization Recommendations
            </h3>
            <p className="text-sm text-muted-foreground">
              AI-powered recommendations based on your historical analysis from {dateInfo.relative}
            </p>
          </div>
          <div className="space-y-4">
            {analysis.recommendations.map((rec, index) => (
              <GEORecommendationCard
                key={index}
                recommendation={rec}
              />
            ))}
            {analysis.recommendations.length === 0 && (
              <div className="text-center py-12">
                <div className="text-muted-foreground">
                  <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">No Recommendations Available</p>
                  <p className="text-sm">
                    No specific recommendations were generated for this historical analysis.
                  </p>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
