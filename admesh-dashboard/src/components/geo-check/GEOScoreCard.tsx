import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

interface GEOScoreCardProps {
  title: string;
  score: number;
  maxScore?: number;
  description: string;
  trend?: "up" | "down" | "stable";
}

export function GEOScoreCard({ 
  title, 
  score, 
  maxScore = 100, 
  description, 
  trend 
}: GEOScoreCardProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return "Excellent";
    if (score >= 60) return "Good";
    return "Needs Improvement";
  };

  return (
    <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-lg transition-all duration-200 group">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wide">
            {title}
          </CardTitle>
          <div className={`w-3 h-3 rounded-full ${
            score >= 80 ? 'bg-green-500' :
            score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
          }`} />
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="text-4xl font-bold">
              <span className={getScoreColor(score)}>{score}</span>
              <span className="text-gray-400 dark:text-gray-500 text-xl">/{maxScore}</span>
            </div>
            <Badge
              variant={getScoreBadgeVariant(score)}
              className={`px-3 py-1 text-sm font-medium ${
                score >= 80 ? 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800' :
                score >= 60 ? 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800' :
                'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800'
              }`}
            >
              {getScoreLabel(score)}
            </Badge>
          </div>

          <div className="space-y-3">
            <Progress
              value={(score / maxScore) * 100}
              className={`h-3 ${
                score >= 80 ? '[&>div]:bg-green-500' :
                score >= 60 ? '[&>div]:bg-yellow-500' :
                '[&>div]:bg-red-500'
              }`}
            />
            <div className="flex items-start gap-2">
              <div className={`h-1.5 w-6 rounded-full mt-1.5 flex-shrink-0 ${
                score >= 80 ? 'bg-gradient-to-r from-green-400 to-emerald-500' :
                score >= 60 ? 'bg-gradient-to-r from-yellow-400 to-orange-500' :
                'bg-gradient-to-r from-red-400 to-pink-500'
              }`}></div>
              <span className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">{description}</span>
            </div>
          </div>

          {trend && (
            <div className="flex items-center gap-2 text-sm pt-3 border-t border-gray-100 dark:border-gray-700">
              {trend === "up" && (
                <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                  <span className="text-lg">↗</span>
                  <span className="font-medium">Improving</span>
                </div>
              )}
              {trend === "down" && (
                <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                  <span className="text-lg">↘</span>
                  <span className="font-medium">Declining</span>
                </div>
              )}
              {trend === "stable" && (
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                  <span className="text-lg">→</span>
                  <span className="font-medium">Stable</span>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
