import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  AlertTriangle,
  CheckCircle,
  Info,
  ExternalLink,
  BookOpen,
  FileText,
  Code,
  Users,
  Award,
  BarChart3,
  Eye
} from "lucide-react";

interface GEORecommendation {
  priority: "high" | "medium" | "low";
  category: string;
  title: string;
  description: string;
  impact: string;
}

interface GEORecommendationCardProps {
  recommendation: GEORecommendation;
  onLearnMore?: () => void;
}

export function GEORecommendationCard({ 
  recommendation, 
  onLearnMore 
}: GEORecommendationCardProps) {
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "high":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case "medium":
        return <Info className="h-4 w-4 text-yellow-600" />;
      case "low":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Info className="h-4 w-4 text-gray-600" />;
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case "high":
        return "destructive";
      case "medium":
        return "secondary";
      case "low":
        return "outline";
      default:
        return "outline";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "border-l-red-500";
      case "medium":
        return "border-l-yellow-500";
      case "low":
        return "border-l-green-500";
      default:
        return "border-l-gray-500";
    }
  };

  const getCategoryIcon = (category: string) => {
    const categoryLower = category.toLowerCase();
    if (categoryLower.includes("structured") || categoryLower.includes("content")) {
      return <FileText className="h-4 w-4 text-blue-600" />;
    } else if (categoryLower.includes("schema") || categoryLower.includes("metadata")) {
      return <Code className="h-4 w-4 text-purple-600" />;
    } else if (categoryLower.includes("brand") || categoryLower.includes("consistency")) {
      return <Users className="h-4 w-4 text-green-600" />;
    } else if (categoryLower.includes("third-party") || categoryLower.includes("authority")) {
      return <Award className="h-4 w-4 text-orange-600" />;
    } else if (categoryLower.includes("topical")) {
      return <BarChart3 className="h-4 w-4 text-indigo-600" />;
    } else if (categoryLower.includes("monitoring") || categoryLower.includes("ai")) {
      return <Eye className="h-4 w-4 text-gray-600" />;
    }
    return <Info className="h-4 w-4 text-gray-600" />;
  };

  const getCategoryColor = (category: string) => {
    const categoryLower = category.toLowerCase();
    if (categoryLower.includes("structured") || categoryLower.includes("content")) {
      return "bg-blue-50 border-blue-200 text-blue-800";
    } else if (categoryLower.includes("schema") || categoryLower.includes("metadata")) {
      return "bg-purple-50 border-purple-200 text-purple-800";
    } else if (categoryLower.includes("brand") || categoryLower.includes("consistency")) {
      return "bg-green-50 border-green-200 text-green-800";
    } else if (categoryLower.includes("third-party") || categoryLower.includes("authority")) {
      return "bg-orange-50 border-orange-200 text-orange-800";
    } else if (categoryLower.includes("topical")) {
      return "bg-indigo-50 border-indigo-200 text-indigo-800";
    } else if (categoryLower.includes("monitoring") || categoryLower.includes("ai")) {
      return "bg-gray-50 border-gray-200 text-gray-800";
    }
    return "bg-gray-50 border-gray-200 text-gray-700";
  };

  return (
    <Card className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-lg transition-all duration-200 border-l-4 ${getPriorityColor(recommendation.priority)}`}>
      <CardHeader className="pb-6">
        <div className="flex items-start justify-between gap-4">
          <div className="flex items-start gap-4 flex-1">
            <div className={`p-3 rounded-lg ${
              recommendation.priority === 'high' ? 'bg-red-50 dark:bg-red-900/20' :
              recommendation.priority === 'medium' ? 'bg-yellow-50 dark:bg-yellow-900/20' : 'bg-green-50 dark:bg-green-900/20'
            }`}>
              {getPriorityIcon(recommendation.priority)}
            </div>
            <div className="flex-1">
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white leading-tight">
                {recommendation.title}
              </CardTitle>
            </div>
          </div>
          <div className="flex flex-col gap-2 items-end">
            <Badge variant="outline" className={`text-xs flex items-center gap-1.5 px-2 py-1 ${getCategoryColor(recommendation.category)}`}>
              {getCategoryIcon(recommendation.category)}
              {recommendation.category}
            </Badge>
            <Badge
              variant={getPriorityBadgeVariant(recommendation.priority)}
              className={`text-xs font-medium px-2 py-1 ${
                recommendation.priority === 'high' ? 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800' :
                recommendation.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800' :
                'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
              }`}
            >
              {recommendation.priority.toUpperCase()}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
          {recommendation.description}
        </p>

        <div className="bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-3">
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wide">Expected Impact:</span>
          </div>
          <p className="text-sm text-gray-900 dark:text-white font-medium leading-relaxed">{recommendation.impact}</p>
        </div>

        <div className="flex items-center gap-3 pt-2">
          <Button
            variant="outline"
            size="sm"
            className="gap-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
            onClick={onLearnMore}
          >
            <BookOpen className="h-3 w-3" />
            Learn More
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
            onClick={() => {
              // Open AI Visibility Guide
              window.open("https://mucker.com/blog/startup-guide-ai-visibility/", "_blank");
            }}
          >
            <ExternalLink className="h-3 w-3" />
            AI Visibility Guide
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
