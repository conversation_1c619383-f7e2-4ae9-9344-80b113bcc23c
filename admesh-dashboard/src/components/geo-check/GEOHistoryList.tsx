import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Calendar,
  BarChart3,
  Eye,
  Download,
  RefreshCw,
  Clock,
  TrendingUp,
  TrendingDown,
  Minus
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface GEOHistoryItem {
  id: string;
  created_at: string;
  overall_score: number;
  website_analyzed: string;
  brand_name: string;
  analyzed_pages_count: number;
  total_queries_simulated: number;
  prompt_mention_rate: number;
  citation_rate: number;
  website_optimization: number;
  sentiment_tone: number;
  analysis_version: string;
}

interface GEOHistoryListProps {
  history: GEOHistoryItem[];
  loading: boolean;
  onViewReport: (analysisId: string) => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  currentAnalysis?: {
    overallScore: number;
  } | null;
}

export function GEOHistoryList({ 
  history, 
  loading, 
  onViewReport, 
  onLoadMore, 
  hasMore = false,
  currentAnalysis 
}: GEOHistoryListProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return "Excellent";
    if (score >= 60) return "Good";
    return "Needs Improvement";
  };

  const getTrendIcon = (currentScore: number, previousScore: number) => {
    if (currentScore > previousScore) {
      return <TrendingUp className="h-4 w-4 text-green-600" />;
    } else if (currentScore < previousScore) {
      return <TrendingDown className="h-4 w-4 text-red-600" />;
    } else {
      return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      relative: formatDistanceToNow(date, { addSuffix: true }),
      absolute: date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };

  if (loading && history.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">Loading analysis history...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (history.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Analysis History</h3>
            <p className="text-muted-foreground text-center max-w-md">
              You haven&apos;t run any GEO analyses yet. Run your first analysis to start tracking your GEO performance over time.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Analysis History</h3>
          <p className="text-sm text-muted-foreground mt-1">
            {history.length} report{history.length !== 1 ? 's' : ''} found • Track your GEO performance over time
          </p>
        </div>
      </div>

      <div className="space-y-4">
        {history.map((item, index) => {
          const dateInfo = formatDate(item.created_at);
          const isLatest = index === 0;
          const previousItem = index < history.length - 1 ? history[index + 1] : null;
          
          return (
            <Card
              key={item.id}
              className={`transition-all duration-200 hover:shadow-lg hover:border-gray-300 dark:hover:border-gray-600 ${
                isLatest ? 'border-blue-200 bg-blue-50/30 dark:bg-blue-950/20 dark:border-blue-800' : 'border-gray-200 dark:border-gray-700'
              }`}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1 space-y-2">
                    {/* Header with date and trend */}
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span title={dateInfo.absolute}>{dateInfo.relative}</span>
                      </div>
                      
                      {isLatest && (
                        <Badge variant="outline" className="text-xs">
                          Latest
                        </Badge>
                      )}
                      
                      {currentAnalysis && isLatest && previousItem && (
                        <div className="flex items-center gap-1">
                          {getTrendIcon(currentAnalysis.overallScore, previousItem.overall_score)}
                          <span className="text-xs text-muted-foreground">
                            vs previous
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Score and metrics */}
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <div className="text-2xl font-bold">
                          <span className={getScoreColor(item.overall_score)}>
                            {item.overall_score}
                          </span>
                          <span className="text-muted-foreground text-sm">/100</span>
                        </div>
                        <Badge variant={getScoreBadgeVariant(item.overall_score)} className="text-xs">
                          {getScoreLabel(item.overall_score)}
                        </Badge>
                      </div>
                      
                      <div className="flex-1 max-w-xs">
                        <Progress value={item.overall_score} className="h-2" />
                      </div>
                    </div>

                    {/* Quick metrics */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                      <div className="flex flex-col">
                        <span className="text-muted-foreground">Prompt Mentions</span>
                        <span className="font-medium">{Math.round(item.prompt_mention_rate)}%</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-muted-foreground">Citations</span>
                        <span className="font-medium">{Math.round(item.citation_rate)}%</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-muted-foreground">Pages Analyzed</span>
                        <span className="font-medium">{item.analyzed_pages_count}</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-muted-foreground">Queries Tested</span>
                        <span className="font-medium">{item.total_queries_simulated}</span>
                      </div>
                    </div>

                    {/* Website info */}
                    <div className="text-xs text-muted-foreground">
                      <span>Website: </span>
                      <span className="font-mono">{item.website_analyzed}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col gap-2 ml-4">
                    <Button
                      onClick={() => onViewReport(item.id)}
                      variant="outline"
                      size="sm"
                      className="gap-2"
                    >
                      <Eye className="h-3 w-3" />
                      View Report
                    </Button>
                    
                    <Button
                      onClick={() => {
                        // TODO: Implement export functionality
                        console.log('Export report:', item.id);
                      }}
                      variant="ghost"
                      size="sm"
                      className="gap-2 text-muted-foreground"
                    >
                      <Download className="h-3 w-3" />
                      Export
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Load More Button */}
      {hasMore && (
        <div className="flex justify-center pt-4">
          <Button
            onClick={onLoadMore}
            variant="outline"
            disabled={loading}
            className="gap-2"
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Clock className="h-4 w-4" />
            )}
            {loading ? "Loading..." : "Load More Reports"}
          </Button>
        </div>
      )}
    </div>
  );
}
