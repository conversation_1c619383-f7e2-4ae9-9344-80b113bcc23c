# File generated for AdMesh PII sanitization functionality tests

"""
Comprehensive test suite for PII sanitization and prompt building functionality.
Tests cover all scenarios: complete PII, partial info, minimal context, PII-only, and edge cases.
"""

import pytest
from admesh.sanitize_and_build import sanitize_and_build, sanitize_user_input
from admesh.sanitizer import P<PERSON><PERSON>anitizer
from admesh.builder import PromptBuilder
from admesh.patterns import is_valid_name, normalize_gender, extract_goal_text


class TestSanitizeAndBuild:
    """Test the main sanitize_and_build function."""
    
    def test_complete_scenario_with_all_pii_and_context(self):
        """Test input with all PII types and complete context."""
        user_input = "Hi, I'm <PERSON><PERSON> (<EMAIL>, call me at (*************). I'm a 27-year-old female building a wellness app."
        
        result = sanitize_and_build(user_input)
        
        assert result["prompt"] == "Suggest tools for a 27-year-old female building a wellness app."
        assert result["removed"]["name"] == "Priya"
        assert result["removed"]["email"] == "<EMAIL>"
        assert result["removed"]["phone"] == "(*************"
        assert result["extracted_context"]["age"] == 27
        assert result["extracted_context"]["gender"] == "female"
        assert result["extracted_context"]["goal"] == "building a wellness app"
    
    def test_partial_information_age_and_goal(self):
        """Test input with age and goal but no name/contact info."""
        user_input = "I'm 30 years old and working on creating a mobile app for fitness tracking."

        result = sanitize_and_build(user_input)

        # Should use age_goal template format
        assert "30" in result["prompt"]
        assert "creating a mobile app for fitness tracking" in result["prompt"]
        assert result["removed"]["name"] is None
        assert result["removed"]["email"] is None
        assert result["removed"]["phone"] is None
        assert result["extracted_context"]["age"] == 30
        assert result["extracted_context"]["gender"] is None
        assert "creating a mobile app for fitness tracking" in result["extracted_context"]["goal"]
    
    def test_minimal_context_goal_only(self):
        """Test input where only goal extraction is possible."""
        user_input = "Looking for tools to help with building an e-commerce website."
        
        result = sanitize_and_build(user_input)
        
        assert "building an e-commerce website" in result["prompt"]
        assert result["removed"]["name"] is None
        assert result["removed"]["email"] is None
        assert result["removed"]["phone"] is None
        assert result["extracted_context"]["age"] is None
        assert result["extracted_context"]["gender"] is None
        assert "building an e-commerce website" in result["extracted_context"]["goal"]
    
    def test_pii_only_no_context(self):
        """Test input with contact information but no contextual data."""
        user_input = "Contact <NAME_EMAIL> or ******-987-6543."
        
        result = sanitize_and_build(user_input)
        
        assert result["prompt"] == "Suggest relevant tools and services."
        assert result["removed"]["name"] is None
        assert result["removed"]["email"] == "<EMAIL>"
        assert result["removed"]["phone"] == "******-987-6543"
        assert result["extracted_context"]["age"] is None
        assert result["extracted_context"]["gender"] is None
        assert result["extracted_context"]["goal"] is None
    
    def test_edge_case_empty_input(self):
        """Test edge case with empty input."""
        result = sanitize_and_build("")
        
        assert result["prompt"] == "Suggest relevant tools and services."
        assert result["removed"]["name"] is None
        assert result["removed"]["email"] is None
        assert result["removed"]["phone"] is None
        assert result["extracted_context"]["age"] is None
        assert result["extracted_context"]["gender"] is None
        assert result["extracted_context"]["goal"] is None
    
    def test_edge_case_none_input(self):
        """Test edge case with None input."""
        result = sanitize_and_build(None)
        
        assert result["prompt"] == "Suggest relevant tools and services."
        assert result["removed"]["name"] is None
        assert result["removed"]["email"] is None
        assert result["removed"]["phone"] is None
    
    def test_edge_case_special_characters(self):
        """Test input with special characters and symbols."""
        user_input = "I'm Alex! @#$%^&*() Building a crypto-trading bot... Age: 25!!!"

        result = sanitize_and_build(user_input)

        assert result["removed"]["name"] == "Alex"
        assert result["extracted_context"]["age"] == 25
        # Goal extraction should be case-insensitive in comparison
        goal = result["extracted_context"]["goal"]
        assert goal and "building a crypto-trading bot" in goal.lower()
    
    def test_multiple_emails_and_phones(self):
        """Test input with multiple emails and phone numbers."""
        user_input = "Reach <NAME_EMAIL> or <EMAIL>. Phone: (************* or ************."
        
        result = sanitize_and_build(user_input)
        
        # Should return the first detected email and phone
        assert result["removed"]["email"] in ["<EMAIL>", "<EMAIL>"]
        assert result["removed"]["phone"] in ["(*************", "************"]
    
    def test_gender_variations(self):
        """Test different gender expressions."""
        test_cases = [
            ("I'm a 25-year-old guy looking for tools", "male"),
            ("I'm a woman building a startup", "female"),
            ("I'm a 30-year-old gentleman", "male"),
            ("I'm a lady working on a project", "female"),
        ]
        
        for user_input, expected_gender in test_cases:
            result = sanitize_and_build(user_input)
            assert result["extracted_context"]["gender"] == expected_gender
    
    def test_age_variations(self):
        """Test different age expressions."""
        test_cases = [
            ("I'm 25 years old", 25),
            ("I am 30", 30),
            ("35-year-old developer", 35),
            ("Age 40", 40),
        ]
        
        for user_input, expected_age in test_cases:
            result = sanitize_and_build(user_input)
            assert result["extracted_context"]["age"] == expected_age
    
    def test_sanitize_user_input_alias(self):
        """Test the alias function works correctly."""
        user_input = "I'm Sarah, 28 years old, building a SaaS platform."
        
        result1 = sanitize_and_build(user_input)
        result2 = sanitize_user_input(user_input)
        
        assert result1 == result2


class TestPIISanitizer:
    """Test the PIISanitizer class directly."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.sanitizer = PIISanitizer()
    
    def test_detect_names(self):
        """Test name detection functionality."""
        test_cases = [
            ("Hi, I'm John Smith", ["John Smith"]),
            ("My name is Alice", ["Alice"]),
            ("This is Bob here", ["Bob"]),
            ("Call me Sarah", ["Sarah"]),
            ("I'm the developer", []),  # Should not detect "the" as name
        ]
        
        for text, expected in test_cases:
            result = self.sanitizer.detect_names(text)
            assert result == expected
    
    def test_detect_emails(self):
        """Test email detection functionality."""
        test_cases = [
            ("Contact <NAME_EMAIL>", ["<EMAIL>"]),
            ("Email: <EMAIL>", ["<EMAIL>"]),
            ("No email here", []),
            ("Multiple emails: a@b.<NAME_EMAIL>", ["<EMAIL>", "<EMAIL>"]),
        ]
        
        for text, expected in test_cases:
            result = self.sanitizer.detect_emails(text)
            assert set(result) == set(expected)
    
    def test_detect_phones(self):
        """Test phone number detection functionality."""
        test_cases = [
            ("Call me at (*************", ["(*************"]),
            ("Phone: ******-987-6543", ["******-987-6543"]),
            ("My number is ************", ["************"]),
            ("No phone here", []),
        ]
        
        for text, expected in test_cases:
            result = self.sanitizer.detect_phones(text)
            # Check if any expected phone is in result (format may vary)
            if expected:
                assert len(result) > 0
            else:
                assert result == []
    
    def test_extract_age(self):
        """Test age extraction functionality."""
        test_cases = [
            ("I'm 25 years old", 25),
            ("I am 30", 30),
            ("35-year-old", 35),
            ("Age 40", 40),
            ("No age here", None),
            ("I'm 150", None),  # Invalid age
        ]
        
        for text, expected in test_cases:
            result = self.sanitizer.extract_age(text)
            assert result == expected
    
    def test_extract_gender(self):
        """Test gender extraction functionality."""
        test_cases = [
            ("I'm a male developer", "male"),
            ("I am a woman", "female"),
            ("I'm a guy", "male"),
            ("I'm a girl", "female"),
            ("No gender here", None),
        ]
        
        for text, expected in test_cases:
            result = self.sanitizer.extract_gender(text)
            assert result == expected
    
    def test_extract_goal(self):
        """Test goal extraction functionality."""
        test_cases = [
            ("I'm building a mobile app", "building a mobile app"),
            ("Working on creating a website", "creating a website"),
            ("Want to develop a game", "develop a game"),
            ("No goal here", None),
        ]
        
        for text, expected in test_cases:
            result = self.sanitizer.extract_goal(text)
            if expected:
                assert expected in result
            else:
                assert result is None


class TestPromptBuilder:
    """Test the PromptBuilder class directly."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.builder = PromptBuilder()
    
    def test_build_prompt_full_context(self):
        """Test prompt building with full context."""
        context = {"age": 25, "gender": "female", "goal": "building a mobile app"}
        result = self.builder.build_prompt(context)
        
        assert "25-year-old" in result
        assert "female" in result
        assert "building a mobile app" in result
    
    def test_build_prompt_partial_context(self):
        """Test prompt building with partial context."""
        context = {"age": 30, "gender": None, "goal": "creating a website"}
        result = self.builder.build_prompt(context)
        
        assert "30" in result
        assert "creating a website" in result
    
    def test_build_prompt_no_context(self):
        """Test prompt building with no context."""
        context = {"age": None, "gender": None, "goal": None}
        result = self.builder.build_prompt(context)
        
        assert result == "Suggest relevant tools and services."
    
    def test_normalize_goal(self):
        """Test goal normalization functionality."""
        test_cases = [
            ("mobile app", "working on mobile app"),  # Corrected expectation
            ("building a website", "building a website"),
            ("working on a project", "working on a project"),
            ("a startup", "working on a startup"),
        ]

        for goal, expected in test_cases:
            result = self.builder.normalize_goal(goal)
            assert result == expected


class TestPatternHelpers:
    """Test helper functions from patterns module."""
    
    def test_is_valid_name(self):
        """Test name validation functionality."""
        test_cases = [
            ("John", True),
            ("John Smith", True),
            ("the", False),  # Common word
            ("A", False),   # Too short
            ("123", False), # Not letters
            ("ACRONYM", False), # All caps
        ]
        
        for name, expected in test_cases:
            result = is_valid_name(name)
            assert result == expected
    
    def test_normalize_gender(self):
        """Test gender normalization functionality."""
        test_cases = [
            ("male", "male"),
            ("man", "male"),
            ("guy", "male"),
            ("female", "female"),
            ("woman", "female"),
            ("girl", "female"),
        ]
        
        for gender, expected in test_cases:
            result = normalize_gender(gender)
            assert result == expected
    
    def test_extract_goal_text(self):
        """Test goal text extraction and cleaning."""
        test_cases = [
            ("building a mobile app.", "building a mobile app"),
            ("creating a website!!!", "creating a website"),
            ("a" * 150, "a" * 100 + "..."),  # Long text truncation
        ]
        
        for text, expected in test_cases:
            result = extract_goal_text(text)
            assert result == expected
