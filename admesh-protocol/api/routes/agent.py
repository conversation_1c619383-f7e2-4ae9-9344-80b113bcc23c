from fastapi import APIRouter, Depends, HTTPException, Query
from firebase_admin import auth as firebase_auth
from firebase.config import get_db
from auth.deps import verify_firebase_token
from google.cloud import firestore
from datetime import datetime, timedelta, timezone
import logging
from typing import Optional, List

router = APIRouter()
db = get_db()
logger = logging.getLogger(__name__)

def get_date_range(time_range: str):
    """Helper to convert time range string to start date"""
    now = datetime.now(timezone.utc)  # Use timezone-aware datetime
    if time_range == "7d":
        return now - timedelta(days=7)
    elif time_range == "30d":
        return now - timedelta(days=30)
    elif time_range == "90d":
        return now - timedelta(days=90)
    else:  # "all" or any other value
        return datetime(2000, 1, 1, tzinfo=timezone.utc)  # A date far in the past with timezone

@router.get("/agent/queries")
async def get_agent_queries(
    time_range: str = "30d",
    limit: int = 50,
    offset: int = 0,
    decoded_token = Depends(verify_firebase_token)
):
    """Get queries handled by the agent"""
    try:
        agent_id = decoded_token["uid"]
        role = decoded_token.get("role")

        if role != "agent":
            raise HTTPException(status_code=403, detail="Only agents can access this endpoint")

        # Get the start date based on time range
        start_date = get_date_range(time_range)

        # Query sessions where this agent was involved
        sessions_ref = db.collection("sessions")

        # Use the composite index for efficient querying
        query = (
            sessions_ref
            .where("agent_id", "==", agent_id)
            .where("created_at", ">=", start_date)
            .order_by("created_at", direction=firestore.Query.DESCENDING)
        )

        # Get total count for pagination
        total_query = query.get()
        total_count = len(total_query)

        # Apply pagination
        paginated_query = query.limit(limit).offset(offset)
        session_docs = paginated_query.stream()

        # Process the results
        queries = []
        for doc in session_docs:
            session_data = doc.to_dict()
            session_id = doc.id

            # Get click and conversion counts for this session
            clicks_query = (
                db.collection("clicks")
                .where("session_id", "==", session_id)
                .where("agent_id", "==", agent_id)
            )
            clicks_count = len(list(clicks_query.stream()))

            conversions_query = (
                db.collection("conversions")
                .where("session_id", "==", session_id)
                .where("agent_id", "==", agent_id)
            )
            conversions_count = len(list(conversions_query.stream()))

            # Format the query data
            queries.append({
                "id": session_id,
                "query": session_data.get("initial_query", ""),
                "timestamp": session_data.get("created_at", ""),
                "user_id": session_data.get("user_id", ""),
                "status": session_data.get("status", "completed"),
                "products_shown": len(session_data.get("product_ids", [])),
                "clicks": clicks_count,
                "conversions": conversions_count,
            })

        return {
            "queries": queries,
            "total": total_count,
            "offset": offset,
            "limit": limit
        }

    except Exception as e:
        logger.exception("Failed to fetch agent queries")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/agent/conversions")
async def get_agent_conversions(
    time_range: str = "30d",
    limit: int = 50,
    offset: int = 0,
    decoded_token = Depends(verify_firebase_token)
):
    """Get conversions attributed to the agent"""
    try:
        agent_id = decoded_token["uid"]
        role = decoded_token.get("role")

        if role != "agent":
            raise HTTPException(status_code=403, detail="Only agents can access this endpoint")

        # Get the start date based on time range
        start_date = get_date_range(time_range)

        # Query conversions for this agent
        conversions_ref = db.collection("conversions")

        # Use the composite index for efficient querying
        query = (
            conversions_ref
            .where("agent_id", "==", agent_id)
            .where("timestamp", ">=", start_date)
            .order_by("timestamp", direction=firestore.Query.DESCENDING)
        )

        # Get total count for pagination
        total_query = query.get()
        total_count = len(total_query)

        # Apply pagination
        paginated_query = query.limit(limit).offset(offset)
        conversion_docs = paginated_query.stream()

        # Process the results
        conversions = []
        for doc in conversion_docs:
            conversion_data = doc.to_dict()

            # Get brand and offer details if available
            brand_name = None
            offer_title = None

            if "brand_id" in conversion_data:
                brand_doc = db.collection("brands").document(conversion_data["brand_id"]).get()
                if brand_doc.exists:
                    brand_data = brand_doc.to_dict()
                    brand_name = brand_data.get("company_name")

            if "offer_id" in conversion_data:
                offer_doc = db.collection("offers").document(conversion_data["offer_id"]).get()
                if offer_doc.exists:
                    offer_data = offer_doc.to_dict()
                    offer_title = offer_data.get("title")

            # Format the conversion data
            conversions.append({
                "id": doc.id,
                "timestamp": conversion_data.get("timestamp", ""),
                "brand_id": conversion_data.get("brand_id", ""),
                "brand_name": brand_name,
                "offer_id": conversion_data.get("offer_id", ""),
                "offer_title": offer_title,
                "value": conversion_data.get("value", 0),
                "currency": conversion_data.get("currency", "USD"),
                "status": conversion_data.get("status", "pending"),
                "event_type": conversion_data.get("event_type", "conversion"),
            })

        return {
            "conversions": conversions,
            "total": total_count,
            "offset": offset,
            "limit": limit
        }

    except Exception as e:
        logger.exception("Failed to fetch agent conversions")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/agent/earnings/{agent_id}")
async def get_agent_earnings_redirect(
    agent_id: str,
    time_range: str = Query("30d", description="Time range: 7d, 30d, 90d, or all"),
    decoded_token = Depends(verify_firebase_token)
):
    """Redirect to the earnings endpoint for backward compatibility"""
    # This is just a redirect to the main earnings endpoint
    from .earnings import get_agent_earnings
    return await get_agent_earnings(agent_id, time_range, decoded_token)

@router.get("/agent/stats")
async def get_agent_stats(
    time_range: str = "30d",
    decoded_token = Depends(verify_firebase_token)
):
    """Get aggregated stats for the agent"""
    try:
        agent_id = decoded_token["uid"]
        role = decoded_token.get("role")

        if role != "agent":
            raise HTTPException(status_code=403, detail="Only agents can access this endpoint")

        # Get the start date based on time range
        start_date = get_date_range(time_range)

        # Get agent profile
        agent_doc = db.collection("agents").document(agent_id).get()
        if not agent_doc.exists:
            raise HTTPException(status_code=404, detail="Agent profile not found")

        agent_data = agent_doc.to_dict()

        # Query sessions for this agent
        sessions_ref = db.collection("sessions")
        sessions_query = (
            sessions_ref
            .where("agent_id", "==", agent_id)
            .where("created_at", ">=", start_date)
        )
        sessions_count = len(list(sessions_query.stream()))

        # Query clicks for this agent
        clicks_ref = db.collection("clicks")
        clicks_query = (
            clicks_ref
            .where("agent_id", "==", agent_id)
            .where("timestamp", ">=", start_date)
        )
        clicks_count = len(list(clicks_query.stream()))

        # Query conversions for this agent
        conversions_ref = db.collection("conversions")
        conversions_query = (
            conversions_ref
            .where("agent_id", "==", agent_id)
            .where("timestamp", ">=", start_date)
        )
        conversion_docs = list(conversions_query.stream())
        conversions_count = len(conversion_docs)

        # Calculate total conversion value
        total_value = sum(doc.to_dict().get("value", 0) for doc in conversion_docs)

        # Get earnings for this agent
        earnings_ref = db.collection("earnings")
        earnings_query = (
            earnings_ref
            .where("agent_id", "==", agent_id)
            .where("timestamp", ">=", start_date)
        )
        earning_docs = list(earnings_query.stream())

        # Calculate total earnings
        total_earnings = sum(doc.to_dict().get("agent_earning", 0) for doc in earning_docs)

        # Calculate conversion rate
        conversion_rate = (conversions_count / clicks_count * 100) if clicks_count > 0 else 0

        return {
            "agent_id": agent_id,
            "trust_score": agent_data.get("trust_score", 0),
            "total_queries": sessions_count,
            "total_clicks": clicks_count,
            "total_conversions": conversions_count,
            "total_value": total_value,
            "total_earnings": total_earnings,
            "conversion_rate": conversion_rate,
            "time_range": time_range
        }

    except Exception as e:
        logger.exception("Failed to fetch agent stats")
        raise HTTPException(status_code=500, detail=str(e))
