from fastapi import APIRouter, Depends, HTTPException
from firebase_admin import auth as firebase_auth
from firebase.config import get_db
from auth.deps import verify_firebase_token
from pydantic import BaseModel
from google.cloud import firestore
import logging

logger = logging.getLogger(__name__)

class UpdateProfilePayload(BaseModel):
    name: str | None = None
    agentName: str | None = None  # Added for chat agent name
    keywords: list[str] | None = None
    logo_url: str | None = None
    industry: str | None = None
    company_name: str | None = None
    payout_account: str | None = None
    domain: str | None = None

class UpdateOnboardingPayload(BaseModel):
    onboardingStatus: str | None = None  # 'pending' | 'completed'
    seenPioneerWelcome: bool | None = None
    xp: int | None = None
    lifetime_xp: int | None = None

router = APIRouter()
db = get_db()

@router.get("/auth/me")
async def get_current_user(decoded_token = Depends(verify_firebase_token)):
    try:
        uid = decoded_token["uid"]
        role = decoded_token.get("role")

        if not role:
            raise HTTPException(status_code=403, detail="No role assigned to user")

        # Initialize data with basic user info from token
        data = {
            "uid": uid,
            "email": decoded_token.get("email", ""),
            "name": decoded_token.get("name", ""),
        }

        # Try to get user document from Firestore
        try:
            base_profile = db.collection("users").document(uid).get()
            if base_profile.exists:
                data.update(base_profile.to_dict())
            else:
                # Create a basic user document if it doesn't exist
                default_user_data = {
                    "uid": uid,
                    "email": decoded_token.get("email", ""),
                    "name": decoded_token.get("name", ""),
                    "created_at": firestore.SERVER_TIMESTAMP,
                }

                if role == "user":
                    default_user_data.update({
                        "onboardingStatus": "pending",
                        "seenPioneerWelcome": False,
                        "xp": 0,
                        "lifetime_xp": 0,
                    })

                db.collection("users").document(uid).set(default_user_data)
                data.update(default_user_data)
        except Exception as e:
            # Log the error but continue with basic data
            logger.error(f"Error accessing user document: {str(e)}")

        # Try to load role-specific data
        try:
            if role == "agent":
                role_doc = db.collection("agents").document(uid).get()
                if role_doc.exists:
                    data.update(role_doc.to_dict())
            elif role == "brand":
                role_doc = db.collection("brands").document(uid).get()
                if role_doc.exists:
                    data.update(role_doc.to_dict())
        except Exception as e:
            # Log the error but continue with basic data
            logger.error(f"Error accessing role-specific document: {str(e)}")

        # Ensure onboarding fields are present for users
        if role == "user":
            # Set default values if fields don't exist
            if "onboardingStatus" not in data:
                data["onboardingStatus"] = "pending"
            if "seenPioneerWelcome" not in data:
                data["seenPioneerWelcome"] = False
            if "xp" not in data:
                data["xp"] = 0
            if "lifetime_xp" not in data:
                data["lifetime_xp"] = data.get("xp", 0)

            # Check if user is eligible for Pioneer program
            try:
                counter_ref = db.collection("counters").document("pioneer_users")
                counter_doc = counter_ref.get()

                if counter_doc.exists:
                    current_count = counter_doc.to_dict().get("count", 0)
                    data["isPioneerEligible"] = current_count < 500
                else:
                    data["isPioneerEligible"] = True  # First users are always eligible
            except Exception as e:
                logger.error(f"Error checking pioneer eligibility: {str(e)}")
                data["isPioneerEligible"] = True  # Default to eligible on error

        return data
    except Exception as e:
        logger.error(f"Error in get_current_user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get user data: {str(e)}")

@router.patch("/auth/update-profile")
async def update_profile(payload: UpdateProfilePayload, decoded_token = Depends(verify_firebase_token)):
    uid = decoded_token["uid"]
    role = decoded_token.get("role")

    if not role:
        raise HTTPException(status_code=403, detail="No role assigned to user")

    update_data = {k: v for k, v in payload.model_dump().items() if v is not None}

    if not update_data:
        raise HTTPException(status_code=400, detail="No fields provided for update")

    # Update role-specific collection
    if role == "agent":
        db.collection("agents").document(uid).update(update_data)
    elif role == "brand":
        db.collection("brands").document(uid).update(update_data)
    else:
        db.collection("users").document(uid).update(update_data)

    return {"status": "success", "updated": update_data}

@router.patch("/auth/update-onboarding")
async def update_onboarding(payload: UpdateOnboardingPayload, decoded_token = Depends(verify_firebase_token)):
    """Update user onboarding status, pioneer welcome flag, and XP"""
    uid = decoded_token["uid"]
    role = decoded_token.get("role")

    if not role:
        raise HTTPException(status_code=403, detail="No role assigned to user")

    # Only users can update onboarding status
    if role != "user":
        raise HTTPException(status_code=403, detail="Only users can update onboarding status")

    update_data = {k: v for k, v in payload.model_dump().items() if v is not None}

    if not update_data:
        raise HTTPException(status_code=400, detail="No fields provided for update")

    # Update user document
    db.collection("users").document(uid).update(update_data)

    return {"status": "success", "updated": update_data}
