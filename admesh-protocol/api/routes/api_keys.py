from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel
from typing import Optional, List, Dict
from firebase.config import get_db
from google.cloud import firestore
from auth.deps import verify_firebase_token, require_role
import logging
import secrets
import string
import time
from cryptography.fernet import Fernet
import os

# Load from env or GCP Secret Manager
FERNET_KEY = os.environ["FERNET_SECRET"]
fernet = Fernet(FERNET_KEY)

def encrypt_api_key(api_key: str) -> str:
    return fernet.encrypt(api_key.encode()).decode()

def decrypt_api_key(encrypted_api_key: str) -> str:
    return fernet.decrypt(encrypted_api_key.encode()).decode()

router = APIRouter()
db = get_db()
logger = logging.getLogger(__name__)

class ApiKeyResponse(BaseModel):
    id: str
    agent_id: str
    key: str
    type: str
    created_at: int
    last_used: Optional[int] = None
    is_active: bool = True
    name: Optional[str] = None

class CreateApiKeyPayload(BaseModel):
    agent_id: str
    type: str = "test"  # "test" or "production"
    name: Optional[str] = None

class ApiKeyListResponse(BaseModel):
    keys: List[ApiKeyResponse]

def generate_api_key(key_type: str) -> str:
    """Generate a unique API key with a prefix based on type"""
    # Define prefix based on key type
    prefix = "sk_test_" if key_type == "test" else "sk_live_"    
    
    # Generate a random string for the key
    alphabet = string.ascii_letters + string.digits
    random_part = ''.join(secrets.choice(alphabet) for _ in range(32))    
    return f"{prefix}{random_part}"

@router.post("/api-keys/create", response_model=ApiKeyResponse)
async def create_api_key(
    payload: CreateApiKeyPayload,
    decoded_token = Depends(require_role("agent"))
):
    """Create a new API key for an agent"""
    uid = decoded_token["uid"]
    try:
        agent_ref = db.collection("agents").document(uid)
        agent_doc = agent_ref.get()
        if not agent_doc.exists:
            raise HTTPException(status_code=404, detail="Agent not found")
        agent_data = agent_doc.to_dict()
        # Use uid as agent_id for all API key operations
        agent_id = uid
        # Choose subcollection based on type
        subcollection = "test_keys" if payload.type == "test" else "prod_keys"
        keys_collection = db.collection("api_keys").document(agent_id).collection(subcollection)
        existing_keys = keys_collection.where("is_active", "==", True).stream()
        for key_doc in existing_keys:
            key_ref = keys_collection.document(key_doc.id)
            key_ref.update({"is_active": False})
        api_key = generate_api_key(payload.type)
        now = int(time.time())
        # Encrypt the API key before storing
        encrypted_api_key = encrypt_api_key(api_key)
        key_data = {
            "agent_id": agent_id,
            "key": encrypted_api_key,  # Store encrypted
            "type": payload.type,
            "created_at": now,
            "last_used": None,
            "is_active": True,
            "name": payload.name or f"{payload.type.capitalize()} API Key"
        }
        key_ref = keys_collection.document()
        key_ref.set(key_data)
        # Update or create agent-level API key metadata
        agent_api_keys_meta_ref = db.collection("api_keys").document(agent_id)
        meta_update = {}
        if payload.type == "test":
            meta_update["current_test_id"] = key_ref.id
        else:
            meta_update["current_prod_id"] = key_ref.id
        agent_api_keys_meta_ref.set(meta_update, merge=True)
        # Return the plaintext key in the response
        return ApiKeyResponse(id=key_ref.id, key=api_key, **{k: v for k, v in key_data.items() if k != 'key'})
    except Exception as e:
        logger.exception(f"Failed to create API key: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create API key: {str(e)}")

@router.get("/api-keys/list", response_model=ApiKeyListResponse)
async def list_api_keys(decoded_token = Depends(require_role("agent"))):
    """List all API keys for an agent"""
    uid = decoded_token["uid"]
    try:
        agent_ref = db.collection("agents").document(uid)
        agent_doc = agent_ref.get()
        if not agent_doc.exists:
            raise HTTPException(status_code=404, detail="Agent not found")
        agent_data = agent_doc.to_dict()
        agent_id = agent_data.get("agent_id")
        if not agent_id:
            raise HTTPException(status_code=400, detail="Agent ID not found")
        # Query both subcollections
        test_keys_collection = db.collection("api_keys").document(agent_id).collection("test_keys")
        prod_keys_collection = db.collection("api_keys").document(agent_id).collection("prod_keys")
        test_keys = []
        for doc in test_keys_collection.stream():
            d = doc.to_dict()
            try:
                d["key"] = decrypt_api_key(d["key"])
            except Exception:
                d["key"] = None
            test_keys.append(ApiKeyResponse(id=doc.id, **d))
        prod_keys = []
        for doc in prod_keys_collection.stream():
            d = doc.to_dict()
            try:
                d["key"] = decrypt_api_key(d["key"])
            except Exception:
                d["key"] = None
            prod_keys.append(ApiKeyResponse(id=doc.id, **d))
        return ApiKeyListResponse(keys=test_keys + prod_keys)
    except Exception as e:
        logger.exception(f"Failed to list API keys: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list API keys: {str(e)}")

@router.post("/api-keys/{key_id}/revoke")
async def revoke_api_key(
    key_id: str,
    decoded_token = Depends(require_role("agent"))
):
    """Revoke an API key"""
    uid = decoded_token["uid"]
    try:
        agent_ref = db.collection("agents").document(uid)
        agent_doc = agent_ref.get()
        if not agent_doc.exists:
            raise HTTPException(status_code=404, detail="Agent not found")
        agent_data = agent_doc.to_dict()
        agent_id = agent_data.get("agent_id")
        # Try both subcollections
        for subcollection in ["test_keys", "prod_keys"]:
            key_ref = db.collection("api_keys").document(agent_id).collection(subcollection).document(key_id)
            key_doc = key_ref.get()
            if key_doc.exists:
                key_data = key_doc.to_dict()
                if key_data.get("agent_id") != agent_id:
                    raise HTTPException(status_code=403, detail="You can only revoke your own API keys")
                key_ref.update({"is_active": False})
                return {"status": "success", "message": "API key revoked"}
        raise HTTPException(status_code=404, detail="API key not found")
    except Exception as e:
        logger.exception(f"Failed to revoke API key: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to revoke API key: {str(e)}")

@router.get("/api-keys/verify")
async def verify_api_key(
    request: Request
):
    """Verify an API key from the Authorization header"""
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing or invalid Authorization header")
    api_key = auth_header.replace("Bearer ", "")
    try:
        agents = db.collection("api_keys").stream()
        found = None
        found_agent_id = None
        found_type = None
        found_key_id = None
        for agent_doc in agents:
            agent_id = agent_doc.id
            for subcollection, key_type in [("test_keys", "test"), ("prod_keys", "production")]:
                keys_collection = db.collection("api_keys").document(agent_id).collection(subcollection)
                key_query = keys_collection.where("is_active", "==", True).limit(10).stream()
                for key_doc in key_query:
                    key_data = key_doc.to_dict()
                    try:
                        decrypted = decrypt_api_key(key_data["key"])
                    except Exception:
                        continue  # skip if decryption fails
                    if decrypted == api_key:
                        found = key_doc
                        found_agent_id = agent_id
                        found_type = key_type
                        found_key_id = key_doc.id
                        break
                if found:
                    break
            if found:
                break
        if not found:
            raise HTTPException(status_code=401, detail="Invalid or revoked API key")
        key_data = found.to_dict()
        now = int(time.time())
        db.collection("api_keys").document(found_agent_id).collection(
            "test_keys" if key_data.get("type") == "test" else "prod_keys"
        ).document(found_key_id).update({"last_used": now})
        return {
            "agent_id": key_data.get("agent_id"),
            "type": key_data.get("type"),
            "is_test": key_data.get("type") == "test"
        }
    except Exception as e:
        logger.exception(f"Failed to verify API key: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to verify API key: {str(e)}")
