from fastapi import APIRouter, Request, Query,Depends
from google.cloud import firestore
from typing import List
from auth.deps import require_role
from firebase.config import get_db
db = get_db()
router = APIRouter()

@router.get("/offers/discover")
async def discover_offers(
    request: Request,
    query: str = Query(..., description="User's intent query"),
    categories: List[str] = Query(default=[]),
    min_score: float = 50.0,
    limit: int = 10,
    user = Depends(require_role("agent"))
):
    offers_ref = db.collection("offers")
    query_ref = offers_ref.where("offer_trust_score", ">=", min_score)

    if categories:
        query_ref = query_ref.where("categories", "array_contains_any", categories)

    results = query_ref.limit(limit).stream()
    offers = []

    for doc in results:
        offer = doc.to_dict()
        if query.lower() in offer["title"].lower() or query.lower() in offer["description"].lower():
            offers.append(offer)

    return {"offers": offers}
