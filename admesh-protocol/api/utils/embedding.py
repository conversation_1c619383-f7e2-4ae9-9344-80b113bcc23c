from openai import OpenAI
import logging
import numpy as np
from typing import List

logger = logging.getLogger(__name__)

# Initialize OpenAI client (automatically uses OPENAI_API_KEY env var)
client = OpenAI()

def embed_text(text: str) -> List[float]:
    """
    Generate embeddings for the given text using OpenAI's text-embedding-3-small model.

    Args:
        text: The text to embed

    Returns:
        List of floats representing the embedding vector

    Raises:
        Exception: If the OpenAI API call fails
    """
    try:
        logger.info(f"🔮 Generating embedding for text: {text[:100]}...")

        response = client.embeddings.create(
            input=text,
            model="text-embedding-3-small"
        )

        embedding = response.data[0].embedding
        logger.info(f"✅ Generated embedding with {len(embedding)} dimensions")

        return embedding

    except Exception as e:
        logger.error(f"❌ Failed to generate embedding: {str(e)}")
        raise Exception(f"Failed to generate embedding: {str(e)}")


def cosine_similarity(a: List[float], b: List[float]) -> float:
    """
    Calculate cosine similarity between two embedding vectors.

    Args:
        a: First embedding vector
        b: Second embedding vector

    Returns:
        Cosine similarity score between 0 and 1
    """
    try:
        a_array = np.array(a)
        b_array = np.array(b)

        # Calculate cosine similarity
        dot_product = np.dot(a_array, b_array)
        norm_a = np.linalg.norm(a_array)
        norm_b = np.linalg.norm(b_array)

        if norm_a == 0 or norm_b == 0:
            return 0.0

        similarity = dot_product / (norm_a * norm_b)

        # Ensure the result is between 0 and 1
        return float(max(0.0, min(1.0, similarity)))

    except Exception as e:
        logger.error(f"❌ Failed to calculate cosine similarity: {str(e)}")
        return 0.0
