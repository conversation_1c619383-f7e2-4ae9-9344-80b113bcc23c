import logging
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi import Request

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Import configuration manager
from config.config_manager import get_config, get_cors_origins, is_debug, get_environment

# Import Firebase initialization
from firebase.config import initialize_firebase

# Import all routers
from api.routes.offers import router as offer_router
from api.routes.click import router as click_router
from api.routes.conversion import router as conversion_router
from api.routes.discover import router as discover_router
from api.routes.earnings import router as earnings_router
from auth.register import router as auth_router
from api.routes.stats import router as stats_router
from api.routes.me import router as me_router
from api.routes.brands import router as brands_router
from api.routes.openrouter import router as openrouter_router
from api.routes.user import router as user_router
from api.routes.products import router as products_router
import api.routes.billing as billing_router
from api.routes.agent import router as agent_router
from auth.admin import router as admin_router
from api.routes.badges import router as badges_router
from api.routes.leaderboard import router as leaderboard_router
from api.routes.agent_automation import router as agent_automation_router
from api.routes.email import router as email_router
from auth.custom_claims import router as custom_claims_router
from api.routes.agent_onboarding import router as agent_onboarding_router
from api.routes.api_keys import router as api_keys_router
from api.routes.agent_recommendation import router as agent_recommendation_router
from api.routes.geo_check import router as geo_check_router

# Configure logging
config = get_config()
logging.basicConfig(
    level=getattr(logging, config.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Log environment information
logger.info(f"🚀 Starting AdMesh Protocol API in {get_environment()} environment")
logger.info(f"🔧 Debug mode: {is_debug()}")
logger.info(f"📦 API Base URL: {config.api_base_url}")

# Create FastAPI app
app = FastAPI(
    title="AdMesh Protocol API",
    description="AdMesh Protocol API for offers, clicks, conversions, and agent earnings.",
    version="1.0.0",
    debug=is_debug()
)

# Initialize Firebase
try:
    initialize_firebase()
    logger.info("✅ Firebase initialized successfully")
except Exception as e:
    logger.error(f"❌ Failed to initialize Firebase: {e}")
    raise

# Configure CORS with environment-specific origins
cors_origins = get_cors_origins()
logger.info(f"🌐 CORS origins: {cors_origins}")

app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# Register all routers
app.include_router(auth_router)  # Adding auth router
app.include_router(openrouter_router, prefix="/openrouter", tags=["OpenRouter"])
app.include_router(offer_router, prefix="/offers", tags=["Offers"])
app.include_router(discover_router, prefix="/discover", tags=["Discover"])
app.include_router(click_router, prefix="/click", tags=["Click Tracking"])
app.include_router(conversion_router, prefix="/conversion", tags=["Conversion Logging"])
app.include_router(earnings_router, prefix="/earnings", tags=["Earnings"])
app.include_router(stats_router, prefix="/api", tags=["Stats"])
app.include_router(me_router, prefix="/api", tags=["Me"])
app.include_router(user_router, prefix="/user", tags=["User"])
app.include_router(products_router, prefix="/products", tags=["Products"])
app.include_router(billing_router.router, prefix="/billing", tags=["Billing"])
app.include_router(brands_router, prefix="/brands", tags=["Brands"])
app.include_router(agent_router, tags=["Agent"])
app.include_router(admin_router, tags=["Admin"])
# Register badges router
app.include_router(badges_router, prefix="/badges", tags=["Badges"])
app.include_router(leaderboard_router, prefix="/leaderboard", tags=["Leaderboard"])
# Register agent automation router
app.include_router(agent_automation_router, tags=["Agent Automation"])
# Register agent onboarding router
app.include_router(agent_onboarding_router, tags=["Agent Onboarding"])
# Register API keys router
app.include_router(api_keys_router, tags=["API Keys"])
# Register email router
app.include_router(email_router, prefix="/email", tags=["Email"])
# Register custom claims router
app.include_router(custom_claims_router, tags=["Custom Claims"])
# Register agent recommendation router
app.include_router(agent_recommendation_router, prefix="/agent", tags=["Agent Recommendation"])
# Register GEO check router
app.include_router(geo_check_router, tags=["GEO Check"])
# Register email verification router
# app.include_router(email_verification_router, tags=["Email Verification"])

# 👇 Add OpenAPI security customization for Swagger
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=app.title,
        version="1.0.0",
        description="AdMesh Protocol API for offers, clicks, conversions, and agent earnings.",
        routes=app.routes,
    )

    openapi_schema["components"]["securitySchemes"] = {
        "FirebaseAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "Paste your Firebase ID token here",
        }
    }

    EXCLUDED_PATHS = [
        "/auth/email-register",
    ]

    for path, methods in openapi_schema["paths"].items():
        if path not in EXCLUDED_PATHS:
            for method in methods.values():
                method["security"] = [{"FirebaseAuth": []}]


    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(_: Request, exc: RequestValidationError):
    print(f"🔥 Validation error: {exc}")
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors(), "body": exc.body},
    )