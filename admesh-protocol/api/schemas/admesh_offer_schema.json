{"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "<PERSON><PERSON><PERSON>", "type": "object", "required": ["id", "title", "description", "url", "suggestion_reason", "reward_note", "payout", "categories", "trust_score", "budget", "brand_id", "created_at"], "properties": {"id": {"type": "string", "description": "UUID of the offer"}, "title": {"type": "string", "description": "Short offer headline"}, "description": {"type": "string", "description": "Expanded details of the offer"}, "url": {"type": "string", "format": "uri", "description": "Target landing page URL"}, "suggestion_reason": {"type": "string", "description": "Why this offer should be suggested to the user"}, "reward_note": {"type": "string", "description": "What the user/agent gets if converted (e.g. 10% cashback)"}, "payout": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string", "default": "USD"}, "model": {"type": "string", "enum": ["CPA", "CPC", "Recurring"]}}, "required": ["amount", "model"]}, "categories": {"type": "array", "items": {"type": "string"}}, "trust_score": {"type": "number", "minimum": 0, "maximum": 100}, "budget": {"type": "number", "description": "Total budget allocated for the offer"}, "brand_id": {"type": "string", "description": "ID of the brand submitting the offer"}, "created_at": {"type": "string", "format": "date-time"}, "valid_until": {"type": "string", "format": "date-time"}, "tracking": {"type": "object", "properties": {"click_url": {"type": "string", "format": "uri"}, "conversion_url": {"type": "string", "format": "uri"}, "pixel": {"type": "string", "format": "uri"}}}, "keywords": {"type": "array", "items": {"type": "string"}, "description": "Any agent-optimizable keywords like 'popular', 'eco-friendly', etc."}, "meta": {"type": "object", "additionalProperties": true}}}