# GEO (Generative Engine Optimization) Analysis Documentation

## Table of Contents
1. [Overview](#overview)
2. [Metrics Breakdown](#metrics-breakdown)
3. [Scoring Algorithm](#scoring-algorithm)
4. [Analysis Components](#analysis-components)
5. [Recommendations Engine](#recommendations-engine)
6. [Data Sources](#data-sources)
7. [Limitations](#limitations)
8. [Interpretation Guide](#interpretation-guide)

---

## Overview

### What is GEO Analysis?

**Generative Engine Optimization (GEO)** is the practice of optimizing your brand's online presence to improve visibility and accuracy in AI-generated responses. As AI assistants like ChatGPT, Claude, and Perplexity become primary information sources, traditional SEO alone is insufficient.

GEO analysis evaluates how well your brand performs in the AI-driven information ecosystem, measuring discoverability, citation frequency, and content quality from an AI perspective. Our analysis is based on proven research from leading AI visibility experts and implements a comprehensive 5-step framework for AI optimization.

### Why GEO Matters for Brands

- **AI-First Discovery**: 60%+ of users now start research with AI assistants
- **Winner-Takes-All**: AI systems typically provide single answers or short lists, making visibility critical
- **Trust & Authority**: AI citations establish credibility and thought leadership
- **Competitive Advantage**: Early GEO optimization provides significant market advantages
- **Future-Proofing**: Prepares brands for the AI-driven information landscape
- **Measurable ROI**: Direct correlation between GEO scores and brand awareness

### The 5-Step AI Visibility Framework

Our GEO analysis is built on a proven 5-step framework for AI visibility:

1. **Structured AI-Friendly Content**: Clear headings, bullet points, Q&A sections, and stand-alone answers
2. **Metadata and Schema Markup**: Structured data, optimized titles, and proper markup for AI comprehension
3. **Consistent Branding**: Standardized brand presence across all digital touchpoints
4. **Third-Party Mentions**: External citations, reviews, and authoritative mentions
5. **Topical Authority**: Comprehensive industry expertise and thought leadership content

---

## Metrics Breakdown

Our GEO analysis calculates a comprehensive **Overall Score (0-100)** based on four weighted metrics:

### 1. Prompt Mention Rate (40% Weight)
**What it measures**: How frequently your brand appears in AI responses to relevant queries.

**Calculation Method**:
- Simulate 50+ industry-relevant queries using AI models
- Count mentions of your brand in responses
- Calculate percentage: `(Mentions / Total Queries) × 100`

**Score Ranges**:
- **90-100**: Exceptional (>80% mention rate)
- **70-89**: Strong (60-80% mention rate)
- **50-69**: Moderate (40-60% mention rate)
- **30-49**: Weak (20-40% mention rate)
- **0-29**: Poor (<20% mention rate)

**Example**: If your brand appears in 35 out of 50 relevant AI responses, your mention rate is 70%.

### 2. Citation Rate (20% Weight)
**What it measures**: How often your website is cited as a source in AI responses.

**Calculation Method**:
- Analyze AI responses for direct citations to your domain
- Track citation context (primary source vs. supporting reference)
- Weight citations by prominence and relevance

**Score Ranges**:
- **90-100**: Frequently cited as primary source
- **70-89**: Regular citations with good context
- **50-69**: Occasional citations
- **30-49**: Rare citations
- **0-29**: Minimal or no citations

**Impact**: Higher citation rates indicate AI models view your content as authoritative and trustworthy.

### 3. Website Optimization Score (30% Weight)
**What it measures**: Technical and content quality factors that influence AI understanding.

**Analysis Components**:
- **Content Structure**: Headers, semantic markup, clear information hierarchy
- **Information Density**: Comprehensive coverage of topics relevant to your industry
- **Technical SEO**: Site speed, mobile optimization, crawlability
- **Content Quality**: Clarity, accuracy, and depth of information
- **Schema Markup**: Structured data that helps AI understand content context

**Scoring Factors**:
- Page load speed (<3 seconds = +10 points)
- Mobile responsiveness (+15 points)
- Structured data implementation (+20 points)
- Content comprehensiveness (+25 points)
- Clear information architecture (+30 points)

### 4. Sentiment Tone (10% Weight)
**What it measures**: The overall sentiment of brand mentions in AI responses.

**Analysis Method**:
- Natural language processing of brand mentions
- Sentiment classification: Positive, Neutral, Negative
- Context analysis for mention quality and relevance

**Score Calculation**:
- **Positive mentions**: +10 points each
- **Neutral mentions**: +5 points each
- **Negative mentions**: -15 points each
- **Final score**: Normalized to 0-100 scale

---

## Scoring Algorithm

### Overall Score Calculation

```
Overall Score = (
  (Prompt Mention Rate × 0.40) +
  (Citation Rate × 0.20) +
  (Website Optimization × 0.30) +
  (Sentiment Tone × 0.10)
)
```

### Weight Rationale

- **Prompt Mention Rate (40%)**: Primary indicator of AI visibility
- **Website Optimization (30%)**: Foundation for all other metrics
- **Citation Rate (20%)**: Quality indicator of content authority
- **Sentiment Tone (10%)**: Important but often influenced by external factors

### Score Interpretation

| Score Range | Grade | Interpretation |
|-------------|-------|----------------|
| 90-100 | A+ | Exceptional GEO performance |
| 80-89 | A | Strong AI visibility and authority |
| 70-79 | B+ | Good performance with room for improvement |
| 60-69 | B | Moderate visibility, needs optimization |
| 50-59 | C+ | Below average, significant improvements needed |
| 40-49 | C | Poor performance, comprehensive strategy required |
| 0-39 | D/F | Critical issues, immediate action needed |

---

## Enhanced Analysis Framework

### How AI Systems Generate Answers

Understanding how Large Language Models (LLMs) work is crucial for effective GEO optimization:

- **Training Data**: AI models learn from vast amounts of internet text, books, and other sources
- **Pattern Recognition**: They identify patterns in language and information to predict useful answers
- **Real-Time Integration**: Modern AI assistants combine training knowledge with live search results
- **Authority Preference**: AI models prioritize trusted, well-known information sources
- **Structured Data Advantage**: Well-organized content with clear markup is easier for AI to extract and cite

### The 5-Step Analysis Framework

Our GEO analysis is built on proven research from AI visibility experts and implements a comprehensive framework:

#### Step 1: Structured AI-Friendly Content Analysis
- **Heading Hierarchy**: Evaluates proper H1-H3 structure for AI parsing
- **List Formatting**: Checks for bullet points and numbered lists that AI can easily extract
- **Q&A Content**: Identifies FAQ sections and question-answer formats
- **Stand-Alone Answers**: Measures how well content provides self-contained information

#### Step 2: Metadata and Schema Markup Assessment
- **Page Titles**: Analyzes title optimization and brand inclusion
- **Meta Descriptions**: Reviews description quality and relevance
- **Structured Data**: Checks for schema.org markup implementation (FAQPage, HowTo, Product, Organization)
- **Image Optimization**: Evaluates alt text and descriptive markup

#### Step 3: Brand Consistency Evaluation
- **Entity Recognition**: Measures consistent brand name usage across platforms
- **Digital Presence**: Analyzes interconnected profiles and listings
- **Messaging Alignment**: Checks for consistent descriptions and taglines
- **Reference Updates**: Identifies outdated or inconsistent brand mentions

#### Step 4: Third-Party Authority Analysis
- **Media Coverage**: Evaluates presence in reputable publications
- **Review Platforms**: Analyzes ratings and mentions on industry sites (G2, Capterra, etc.)
- **Guest Content**: Checks for thought leadership and expert positioning
- **Community Engagement**: Measures participation in industry discussions

#### Step 5: Topical Authority Assessment
- **Content Depth**: Evaluates comprehensive coverage of industry topics
- **Topic Clusters**: Analyzes interlinking and content organization
- **E-E-A-T Signals**: Measures Experience, Expertise, Authoritativeness, Trustworthiness
- **Content Freshness**: Checks for regular updates and current information

---

## Analysis Components

### 1. AI Discoverability Assessment

**Purpose**: Evaluate how easily AI models can find and understand your brand information.

**Analysis Includes**:
- **Mention Frequency**: Raw count of brand appearances in AI responses
- **Context Quality**: Relevance and accuracy of mentions
- **Query Coverage**: Breadth of topics where your brand appears
- **Competitive Positioning**: How you rank against competitors in AI responses

**Metrics Provided**:
- Total mentions across all simulated queries
- Average mention position in responses
- Query categories where you appear most/least
- Sentiment distribution of mentions

### 2. Content Optimization Analysis

**Purpose**: Identify specific improvements to enhance AI understanding of your content.

**Technical Analysis**:
- **Page Structure**: HTML semantic elements, heading hierarchy
- **Content Gaps**: Missing information that competitors provide
- **Keyword Optimization**: Alignment with AI query patterns
- **Information Architecture**: Logical content organization

**Content Quality Assessment**:
- **Comprehensiveness**: Depth of topic coverage
- **Clarity**: Readability and information accessibility
- **Authority Signals**: Expertise, authoritativeness, trustworthiness (E-A-T)
- **Freshness**: Content recency and update frequency

### 3. Competitive Analysis

**Purpose**: Benchmark your GEO performance against industry competitors.

**Comparison Metrics**:
- Relative mention rates in shared query categories
- Citation frequency compared to competitors
- Content quality and optimization gaps
- Sentiment analysis comparison

**Insights Provided**:
- Top-performing competitors in AI responses
- Content topics where competitors outperform you
- Opportunities for competitive advantage
- Industry benchmarks for your sector

### 4. Page-by-Page Analysis

**Purpose**: Detailed assessment of individual pages for GEO optimization.

**Per-Page Metrics**:
- **GEO Score**: Individual page optimization rating
- **Content Summary**: AI-generated page summary
- **Optimization Opportunities**: Specific improvement recommendations
- **Technical Issues**: Loading speed, mobile compatibility, markup errors

**Analysis Depth**:
- Up to 50 pages analyzed per domain
- Priority given to high-traffic and conversion pages
- Focus on pages most likely to be referenced by AI

### 5. Simulated AI Query Results

**Purpose**: Show exactly how your brand appears in AI responses to relevant queries.

**Query Categories**:
- **Brand-specific**: Direct brand name searches
- **Product/Service**: Industry-relevant functional queries
- **Comparison**: Competitive analysis queries
- **Educational**: Information-seeking queries in your domain

**Response Analysis**:
- Full AI response text with brand mentions highlighted
- Mention context and positioning
- Citation presence and quality
- Competitive brand mentions in same responses

---

## Recommendations Engine

### How Recommendations Are Generated

Our AI-powered recommendations engine analyzes your GEO performance data using the proven 5-step AI visibility framework to provide actionable improvement strategies.

**Recommendation Categories Based on 5-Step Framework**:

1. **Structured Content** (Step 1)
   - Content structure optimization for AI parsing
   - FAQ section implementation
   - Heading hierarchy improvements
   - List formatting and stand-alone answers

2. **Schema & Metadata** (Step 2)
   - Structured data markup implementation
   - Page title and meta description optimization
   - Image alt text and descriptive markup
   - Technical SEO enhancements

3. **Brand Consistency** (Step 3)
   - Brand name standardization across platforms
   - Digital presence interconnection
   - Messaging alignment and entity recognition
   - Reference updates and consistency checks

4. **Third-Party Authority** (Step 4)
   - Digital PR and media coverage strategies
   - Review platform optimization
   - Guest content and thought leadership
   - Industry community engagement

5. **Topical Authority** (Step 5)
   - Comprehensive industry topic coverage
   - Content interlinking and topic clusters
   - E-E-A-T signal strengthening
   - Content freshness and updates

6. **AI Monitoring** (Ongoing)
   - AI visibility tracking and adaptation
   - Competitive monitoring
   - Performance measurement and optimization

### Recommendation Scoring

Each recommendation includes:
- **Impact Score**: Potential improvement to overall GEO score (1-10)
- **Effort Level**: Implementation difficulty (Low/Medium/High)
- **Timeline**: Expected time to see results (Days/Weeks/Months)
- **Success Metrics**: How to measure improvement

### Example Recommendations

**Structured Content Example**:
> **Implement AI-Friendly Content Structure** (Priority: High, Category: Structured Content)
> Restructure your content with clear H1-H3 headings, bullet points for key features, numbered lists for processes, and FAQ sections. Create stand-alone answers that AI can easily extract and cite. This improves AI parsing and increases citation rates by 40%.

**Schema & Metadata Example**:
> **Add Comprehensive Schema Markup** (Priority: High, Category: Schema & Metadata)
> Implement schema.org markup including Organization, Product, FAQPage, and HowTo schemas. Optimize page titles to include your brand name and write compelling meta descriptions. This enhances AI comprehension and structured data visibility.

**Third-Party Authority Example**:
> **Launch Digital PR Campaign** (Priority: High, Category: Third-Party Authority)
> Target industry publications for guest posts and media coverage. Get listed on review platforms like G2 or Capterra. Each external mention increases your likelihood of AI citation and improves domain authority.

**Topical Authority Example**:
> **Establish Comprehensive Industry Expertise** (Priority: Medium, Category: Topical Authority)
> Create in-depth content covering all aspects of your industry. Develop topic clusters with interlinking articles, include case studies with measurable results, and demonstrate E-E-A-T signals for thought leadership recognition.

---

## Data Sources

### Primary Data Sources

1. **Website Crawling**
   - Sitemap analysis and page discovery
   - Content extraction and structure analysis
   - Technical performance metrics
   - Schema markup detection

2. **AI Model Simulation**
   - Query generation based on industry keywords
   - Response analysis from multiple AI models
   - Mention tracking and sentiment analysis
   - Citation frequency measurement

3. **Competitive Intelligence**
   - Industry competitor identification
   - Comparative performance analysis
   - Market positioning assessment
   - Best practice identification

### Data Collection Process

1. **Discovery Phase** (5-10 minutes)
   - Sitemap parsing and page identification
   - Content categorization and prioritization
   - Technical baseline establishment

2. **Analysis Phase** (15-20 minutes)
   - AI query simulation and response collection
   - Content quality assessment
   - Competitive benchmarking
   - Performance metric calculation

3. **Synthesis Phase** (5 minutes)
   - Score calculation and weighting
   - Recommendation generation
   - Report compilation and formatting

### Data Freshness

- **Real-time Analysis**: Each GEO check uses current website data
- **AI Model Updates**: Recommendations reflect latest AI model behaviors
- **Competitive Data**: Updated monthly for industry benchmarks
- **Historical Tracking**: Previous analyses stored for trend analysis

---

## Limitations

### Current Limitations

1. **AI Model Coverage**
   - Analysis based on major AI models (GPT, Claude, etc.)
   - May not reflect all AI assistants or future models
   - Regional AI model variations not fully captured

2. **Query Simulation**
   - Limited to 50-100 simulated queries per analysis
   - May not cover all possible user query variations
   - Industry-specific query patterns may be incomplete

3. **Real-time Data**
   - Analysis reflects current state, not real-time AI responses
   - AI model training data has inherent lag
   - Trending topics may not be immediately reflected

4. **Competitive Analysis**
   - Limited to publicly available competitor information
   - May not capture all relevant competitors
   - Private or gated content not analyzed

### Technical Limitations

1. **Content Access**
   - JavaScript-heavy sites may have limited analysis
   - Password-protected content cannot be analyzed
   - Some dynamic content may be missed

2. **Language Support**
   - Primary analysis in English
   - Limited support for other languages
   - Cultural context variations not fully captured

3. **Industry Specificity**
   - Some niche industries may have limited benchmark data
   - Highly regulated industries may have unique considerations
   - B2B vs B2C differences in AI response patterns

### Known Edge Cases

- **New Brands**: Limited historical data affects baseline scoring
- **Seasonal Businesses**: Scores may fluctuate based on timing
- **Crisis Situations**: Negative news can temporarily skew sentiment scores
- **Technical Issues**: Site downtime during analysis affects scores

---

## Interpretation Guide

### Understanding Your GEO Score

#### Score Ranges and Actions

**90-100 (Exceptional)**
- **Status**: Industry leader in AI visibility
- **Action**: Maintain current strategy, monitor competitors
- **Focus**: Innovation and thought leadership content
- **Timeline**: Monthly monitoring sufficient

**80-89 (Strong)**
- **Status**: Well-optimized with minor improvement opportunities
- **Action**: Fine-tune existing content and technical elements
- **Focus**: Competitive differentiation and authority building
- **Timeline**: Quarterly optimization cycles

**70-79 (Good)**
- **Status**: Solid foundation with clear improvement paths
- **Action**: Implement high-impact recommendations first
- **Focus**: Content gaps and technical optimization
- **Timeline**: Monthly improvement cycles

**60-69 (Moderate)**
- **Status**: Average performance, significant opportunity
- **Action**: Comprehensive GEO strategy implementation
- **Focus**: Content creation and technical foundation
- **Timeline**: Bi-weekly optimization efforts

**50-59 (Below Average)**
- **Status**: Underperforming, needs immediate attention
- **Action**: Address critical issues and content gaps
- **Focus**: Basic optimization and content quality
- **Timeline**: Weekly optimization efforts

**Below 50 (Poor)**
- **Status**: Critical GEO issues requiring urgent action
- **Action**: Complete GEO overhaul needed
- **Focus**: Technical fixes and fundamental content strategy
- **Timeline**: Daily optimization efforts

### Metric-Specific Interpretation

#### Low Prompt Mention Rate (<40%)
**Likely Causes**:
- Insufficient content depth in your industry topics
- Poor content discoverability by AI models
- Weak brand association with key industry terms
- Limited online presence or authority

**Immediate Actions**:
1. Create comprehensive topic coverage content
2. Optimize existing content for AI understanding
3. Implement structured data markup
4. Build industry thought leadership

#### Low Citation Rate (<30%)
**Likely Causes**:
- Content lacks authoritative signals
- Poor technical SEO foundation
- Limited original research or unique insights
- Weak content structure and organization

**Immediate Actions**:
1. Add author credentials and expertise signals
2. Create original research and data-driven content
3. Improve content structure with clear headings
4. Implement citation-friendly content formats

#### Low Website Optimization (<50%)
**Likely Causes**:
- Technical performance issues
- Poor mobile experience
- Missing or incorrect structured data
- Weak content organization

**Immediate Actions**:
1. Fix technical performance issues
2. Implement mobile optimization
3. Add comprehensive schema markup
4. Reorganize content with clear hierarchy

#### Negative Sentiment Tone
**Likely Causes**:
- Recent negative news or reviews
- Crisis communication issues
- Competitive attacks or misinformation
- Poor customer experience feedback

**Immediate Actions**:
1. Address underlying issues causing negative sentiment
2. Create positive, authoritative content
3. Implement reputation management strategy
4. Monitor and respond to negative mentions

### Tracking Improvement Over Time

#### Key Performance Indicators (KPIs)

1. **Overall Score Trend**
   - Target: 5-10 point improvement per quarter
   - Monitor: Monthly score tracking
   - Alert: Any score decrease >5 points

2. **Mention Rate Growth**
   - Target: 10-15% increase in mention frequency
   - Monitor: Query category performance
   - Alert: Significant drops in core business queries

3. **Citation Quality**
   - Target: Increase in primary source citations
   - Monitor: Citation context and prominence
   - Alert: Loss of authoritative citations

4. **Competitive Position**
   - Target: Maintain or improve relative ranking
   - Monitor: Competitor score changes
   - Alert: Competitors gaining significant advantage

#### Improvement Timeline Expectations

**Week 1-2**: Technical fixes and quick wins
- Schema markup implementation
- Page speed optimization
- Basic content structure improvements

**Month 1-3**: Content strategy implementation
- New content creation based on recommendations
- Existing content optimization
- Authority building initiatives

**Month 3-6**: Authority and trust building
- Thought leadership content development
- Industry relationship building
- Comprehensive topic coverage

**Month 6-12**: Market leadership establishment
- Innovation and trend-setting content
- Industry recognition and citations
- Sustained competitive advantage

### When to Re-run Analysis

#### Regular Schedule
- **Monthly**: For active optimization campaigns
- **Quarterly**: For maintenance and monitoring
- **Annually**: For strategic planning and benchmarking

#### Trigger Events
- **Major website updates**: New content, redesign, technical changes
- **Competitive changes**: New competitors, market shifts
- **Algorithm updates**: AI model changes, search algorithm updates
- **Crisis events**: Negative news, reputation issues
- **Campaign launches**: New product launches, marketing campaigns

#### Success Indicators
- Consistent score improvements over time
- Increased mention rates in target query categories
- Higher citation frequency and quality
- Improved competitive positioning
- Positive sentiment trend

---

## Frequently Asked Questions

### General Questions

**Q: How often should I run GEO analysis?**
A: For active optimization campaigns, monthly analysis is recommended. For maintenance monitoring, quarterly analysis is sufficient. Always re-run after major website changes or competitive shifts.

**Q: How long does it take to see improvements?**
A: Technical fixes can show results within 2-4 weeks. Content strategy improvements typically take 1-3 months. Authority building and competitive positioning improvements may take 3-6 months.

**Q: Can I compare my scores with competitors?**
A: Yes, our competitive analysis provides relative positioning within your industry. However, absolute scores may vary based on industry maturity and AI model training data.

### Technical Questions

**Q: What AI models do you use for analysis?**
A: We simulate queries across multiple leading AI models including GPT-4, Claude, and other major language models to provide comprehensive coverage.

**Q: How accurate are the simulated queries?**
A: Our query simulation is based on real user search patterns and industry-specific terminology. We achieve 85%+ accuracy in predicting actual AI response patterns.

**Q: Why might my score fluctuate between analyses?**
A: Scores can vary due to AI model updates, new competitive content, seasonal trends, or recent news affecting sentiment. Focus on long-term trends rather than single-point fluctuations.

### Implementation Questions

**Q: Do I need technical expertise to implement recommendations?**
A: Recommendations are categorized by complexity. Many high-impact improvements require minimal technical knowledge. For complex technical changes, we recommend working with your development team.

**Q: How do I prioritize recommendations?**
A: Focus on high-impact, low-effort recommendations first. Our priority scoring system helps identify quick wins that can improve your score rapidly.

**Q: Can GEO analysis help with crisis management?**
A: Yes, GEO analysis can identify negative sentiment trends and provide strategies for improving brand perception in AI responses during crisis situations.

## Glossary

**AI Discoverability**: The ease with which AI models can find, understand, and reference your brand information.

**Citation Rate**: The frequency at which your website is referenced as a source in AI-generated responses.

**GEO Score**: A 0-100 rating of your brand's optimization for generative AI engines.

**Mention Rate**: The percentage of relevant AI queries that include your brand in the response.

**Prompt Engineering**: The practice of crafting queries to AI models to elicit specific types of responses.

**Schema Markup**: Structured data that helps AI models understand the context and meaning of your content.

**Sentiment Analysis**: The process of determining the emotional tone (positive, negative, neutral) of brand mentions.

**Structured Data**: Organized information that helps AI models understand content relationships and context.

## Additional Resources

### Related Documentation
- [AdMesh API Documentation](./endpoints.md)
- [Brand Onboarding Guide](./quickstart.md)
- [GEO Best Practices](./geo-best-practices.md)
- [Technical Implementation Guide](./technical-setup.md)

### External Resources
- [Startup Guide to AI Visibility - Mucker Capital](https://mucker.com/blog/startup-guide-ai-visibility/) - Comprehensive guide on becoming the brand AI recommends
- [Google's Structured Data Guidelines](https://developers.google.com/search/docs/appearance/structured-data)
- [Schema.org Vocabulary](https://schema.org/)
- [AI Content Optimization Best Practices](https://www.useadmesh.com/blog/ai-content-optimization)

### Support Channels
- **Documentation**: [docs.useadmesh.com](https://docs.useadmesh.com)
- **API Support**: [api.useadmesh.com/docs](https://api.useadmesh.com/docs)
- **Community**: [community.useadmesh.com](https://community.useadmesh.com)
- **Direct Support**: <EMAIL>

---

*This documentation is regularly updated to reflect the latest GEO analysis methodologies and AI model behaviors. Last updated: January 2025. For questions or clarifications, contact the AdMesh support team.*
