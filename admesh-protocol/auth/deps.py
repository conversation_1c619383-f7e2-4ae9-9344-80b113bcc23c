from fastapi import Request, HTTPException, Depends
from firebase_admin import auth as firebase_auth
from typing import Optional

def verify_firebase_token(request: Request):
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing or invalid Firebase token")

    token = auth_header.replace("Bearer ", "")
    try:
        decoded_token = firebase_auth.verify_id_token(token)
        return decoded_token
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid Firebase token")

def require_role(required_role: str):
    def dependency(decoded_token = Depends(verify_firebase_token)):
        user_role = decoded_token.get("role")
        if user_role != required_role:
            raise HTTPException(status_code=403, detail=f"{required_role} access required")
        return decoded_token
    return dependency

async def optional_verify_token(request: Request) -> Optional[dict]:
    import logging
    logger = logging.getLogger(__name__)

    auth_header = request.headers.get("Authorization")
    logger.info(f"Auth header present: {bool(auth_header)}")

    if auth_header and auth_header.startswith("Bearer "):
        try:
            token = auth_header.replace("Bearer ", "")
            logger.info(f"Token length: {len(token)}")
            decoded_token = firebase_auth.verify_id_token(token)
            logger.info(f"Token verified successfully for user: {decoded_token.get('uid')}")
            return decoded_token
        except Exception as e:
            logger.error(f"Token verification failed: {str(e)}")
            return None
    else:
        logger.warning("No valid Authorization header found")
    return None