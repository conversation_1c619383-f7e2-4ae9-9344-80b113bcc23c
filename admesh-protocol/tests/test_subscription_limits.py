import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from api.main import app
from api.utils.subscription_limits import (
    check_offer_limit,
    update_offer_count,
    get_subscription_document
)
from api.models.subscription import SubscriptionDocument, SubscriptionLimits, SubscriptionUsage

client = TestClient(app)

# Mock subscription document for testing
@pytest.fixture
def mock_subscription_doc():
    return SubscriptionDocument(
        id="test-subscription",
        plan_id="free",
        billing_cycle="free",
        status="active",
        limits=SubscriptionLimits(
            product_listings=1,
            active_offers_per_product=1,
            keyword_limit=10,
            multi_user_limit=0
        ),
        usage=SubscriptionUsage(
            product_listings=1,
            active_offers={"product1": 0},
            keywords_used=0,
            multi_users=0
        )
    )

# Test check_offer_limit function
@pytest.mark.asyncio
async def test_check_offer_limit_under_limit(mock_subscription_doc):
    with patch('api.utils.subscription_limits.get_subscription_document') as mock_get_doc:
        mock_get_doc.return_value = mock_subscription_doc

        # Test when under limit
        result = await check_offer_limit("brand1", "product1")
        assert result is True

@pytest.mark.asyncio
async def test_check_offer_limit_at_limit(mock_subscription_doc):
    # Set usage to be at the limit
    mock_subscription_doc.usage.active_offers["product1"] = 1

    with patch('api.utils.subscription_limits.get_subscription_document') as mock_get_doc:
        mock_get_doc.return_value = mock_subscription_doc

        # Test when at limit
        result = await check_offer_limit("brand1", "product1")
        assert result is False

@pytest.mark.asyncio
async def test_check_offer_limit_unlimited(mock_subscription_doc):
    # Set limit to unlimited (-1)
    mock_subscription_doc.limits.active_offers_per_product = -1

    with patch('api.utils.subscription_limits.get_subscription_document') as mock_get_doc:
        mock_get_doc.return_value = mock_subscription_doc

        # Test with unlimited offers
        result = await check_offer_limit("brand1", "product1")
        assert result is True

# Test update_offer_count function
@pytest.mark.asyncio
async def test_update_offer_count(mock_subscription_doc):
    with patch('api.utils.subscription_limits.get_subscription_document') as mock_get_doc, \
         patch('api.utils.subscription_limits.db') as mock_db:
        mock_get_doc.return_value = mock_subscription_doc

        # Mock Firestore document reference and get method
        mock_sub_ref = MagicMock()
        mock_sub_doc = MagicMock()
        mock_sub_doc.exists = True
        mock_sub_ref.get.return_value = mock_sub_doc

        # Mock db.collection().document().collection().document()
        mock_db.collection.return_value.document.return_value.collection.return_value.document.return_value = mock_sub_ref

        # Test updating offer count
        await update_offer_count("brand1", "product1", 1)

        # Verify the update was called with correct parameters
        mock_sub_ref.update.assert_called_once()
        update_args = mock_sub_ref.update.call_args[0][0]
        assert "usage.active_offers.product1" in update_args
        assert update_args["usage.active_offers.product1"] == 1

# Integration test for offer activation with subscription limits
@pytest.mark.asyncio
async def test_offer_activation_with_limits():
    # This would be an integration test that mocks the entire flow
    # from activating an offer to checking limits and updating counts
    pass
